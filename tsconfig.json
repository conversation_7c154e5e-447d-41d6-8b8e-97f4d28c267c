{
  "exclude": ["./coverage", "./dist", "__tests__", "jest.config.js"],
  "compilerOptions": {
    "target": "esnext",
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "experimentalDecorators": true,
    "allowJs": false,
    "checkJs": false,
    "outDir": "dist",
    "strict": false,
    "strictNullChecks": true,
    "skipLibCheck": true,
    "noImplicitThis": true,
    "esModuleInterop": true,
    "declaration": true,
    "resolveJsonModule": true,
    "lib": ["es2020", "esnext.array", "DOM", "ES2021.String"]
  },
  "include": ["src/**/*.ts", "src/**/*.json"],
  "ts-node": {
    "compilerOptions": {
      "esModuleInterop": true,
    }
  }
}

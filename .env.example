# WEBHOOK_URL=http://localhost:3000/webhooks/whatsapp
# WEBHOOK_TOKEN=GoGnZZGn6DRtUQoWnUzgxDZL
# WEBHOOK_HEADER=api_access_token
IGNORE_GROUP_MESSAGES=true
IGNORE_BROADCAST_STATUSES=true

STORAGE_BUCKET_NAME=unoapi
STORAGE_ACCESS_KEY_ID=my-minio
STORAGE_SECRET_ACCESS_KEY=2NVQWHTTT3asdasMgqapGchy6yAMZn
STORAGE_REGION=us-east-1
STORAGE_ENDPOINT=http://localhost:9000
STORAGE_FORCE_PATH_STYLE=true

MINIO_SERVER_URL=http://localhost:9000
MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
MINIO_SITE_REGION=$STORAGE_REGION
MINIO_ROOT_USER=$STORAGE_ACCESS_KEY_ID
MINIO_ROOT_PASSWORD=$STORAGE_SECRET_ACCESS_KEY
<PERSON>OAPI_AUTH_TOKEN=jsdoijohiewr948hwodjqsjdjldasdlk

REJECT_CALLS=Oi, não consigo atender ligações no whatsapp, poderia me mandar uma mensagem?
REJECT_CALLS_WEBHOOK=Eu estava te ligando no whatsapp...
SEND_CONNECTION_STATUS=true

# Name that will be displayed on smartphone connection
CONFIG_SESSION_PHONE_CLIENT=Unoapi

# Browser Name = Chrome | Firefox | Edge | Opera | Safari

CONFIG_SESSION_PHONE_NAME=Chrome

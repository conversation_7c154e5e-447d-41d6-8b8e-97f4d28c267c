<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Creator: CorelDRAW 2020 (64 Bit) -->
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="361.244mm" height="180.622mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
viewBox="0 0 36124.44 18062.22"
 xmlns:xlink="http://www.w3.org/1999/xlink"
 xmlns:xodm="http://www.corel.com/coreldraw/odm/2003">
 <defs>
  <font id="FontID0" horiz-adv-x="400" font-variant="normal" style="fill-rule:nonzero" font-weight="400">
	<font-face 
		font-family="Bebas Neue">
		<font-face-src>
			<font-face-name name="Bebas Neue Regular"/>
		</font-face-src>
	</font-face>
   <missing-glyph><path d="M0 0z"/></missing-glyph>
   <glyph unicode="A" horiz-adv-x="400" d="M12.0003 0l102 0 20 137.001 125 0 0 1.99953 20 -139 109.999 0 -114 700 -148.999 0 -114 -700zm135 232l47.9997 346 2.00109 0 48.9995 -346 -99.0002 0z"/>
   <glyph unicode="i" horiz-adv-x="192" d="M40.9998 0l110.001 0 0 700 -110.001 0 0 -700z"/>
   <glyph unicode="n" horiz-adv-x="426" d="M40.9998 0l98.0005 0 0 511.001 1.99953 0 132 -511.001 113 0 0 700 -98.0005 0 0 -419 -1.99953 0 -107 419 -138 0 0 -700z"/>
   <glyph unicode="o" horiz-adv-x="399" d="M200 -10.0008c108,0 167,64.0006 167,176.001l0 368c0,112 -59.0002,176.001 -167,176.001 -108,0 -167,-64.0006 -167,-176.001l0 -368c0,-112 59.0002,-176.001 167,-176.001zm0 100.002c-34.9996,0 -57.0007,18.9987 -57.0007,68.9995l0 382c0,50.0008 22.0011,69.001 57.0007,69.001 34.9996,0 57.0007,-19.0002 57.0007,-69.001l0 -382c0,-50.0008 -22.0011,-68.9995 -57.0007,-68.9995z"/>
   <glyph unicode="p" horiz-adv-x="386" d="M40.9998 0l110.001 0 0 285 51.9988 0c110.001,0 164.001,60.9998 164.001,173l0 68.9995c0,112 -53.9998,173 -164.001,173l-162 0 0 -700zm110.001 385l0 215 51.9988 0c35.0012,0 54.0014,-15.9994 54.0014,-66.0002l0 -82.9993c0,-50.0008 -19.0002,-66.0002 -54.0014,-66.0002l-51.9988 0z"/>
   <glyph unicode="u" horiz-adv-x="401" d="M201 -10.0008c108,0 164.001,64.0006 164.001,176.001l0 534 -106 0 0 -542c0,-50.0008 -20.9998,-67.9997 -56.0009,-67.9997 -34.9996,0 -55.9994,17.9989 -55.9994,67.9997l0 542 -109.999 0 0 -534c0,-112 55.9994,-176.001 163.999,-176.001z"/>
  </font>
  <style type="text/css">
   <![CDATA[
    @font-face { font-family:"Bebas Neue";font-variant:normal;font-weight:normal;src:url("#FontID0") format(svg)}
    .str0 {stroke:#373435;stroke-width:20;stroke-miterlimit:22.9256}
    .fil0 {fill:#FEFEFE}
    .fil1 {fill:#189F0F}
    .fil2 {fill:url(#id0)}
    .fil3 {fill:url(#id1)}
    .fil4 {fill:url(#id2);fill-rule:nonzero}
    .fnt0 {font-weight:normal;font-size:6421.5px;font-family:'Bebas Neue'}
   ]]>
  </style>
  <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="7306.57" y1="1610.83" x2="31297.39" y2="12398.03">
   <stop offset="0" style="stop-opacity:1; stop-color:#96EDC0"/>
   <stop offset="0.439216" style="stop-opacity:1; stop-color:#72DA89"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#4DC752"/>
  </linearGradient>
  <linearGradient id="id1" gradientUnits="userSpaceOnUse" x1="-3148.5" y1="-2803.23" x2="15104.97" y2="16451.21">
   <stop offset="0" style="stop-opacity:1; stop-color:#A9F3DA"/>
   <stop offset="0.101961" style="stop-opacity:1; stop-color:#A0F1CE"/>
   <stop offset="0.188235" style="stop-opacity:1; stop-color:#96EEC1"/>
   <stop offset="0.658824" style="stop-opacity:1; stop-color:#37DF49"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#37DF49"/>
  </linearGradient>
  <linearGradient id="id2" gradientUnits="userSpaceOnUse" x1="13954.32" y1="11005.21" x2="38716.71" y2="13624.87">
   <stop offset="0" style="stop-opacity:1; stop-color:#96EDC0"/>
   <stop offset="0.231373" style="stop-opacity:1; stop-color:#6AE487"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#3DDA4D"/>
  </linearGradient>
 </defs>
 <g id="Camada_x0020_1">
  <metadata id="CorelCorpID_0Corel-Layer"/>
  <rect class="fil0 str0" x="3726.91" y="3850.43" width="9075.95" height="9656.67" rx="2192.26" ry="1867.26"/>
  <path class="fil1" d="M12802.86 5906l0 4896.08c146.2,-285.41 307.57,-629.58 305.44,-768.35 23.28,-27.48 -1.23,26.69 28.24,-43.67 5.47,-13.06 8.97,-31.49 13.5,-48.44 8.79,-32.9 16.67,-61.56 27.31,-97.67l134.89 -626.7c24,-126.2 87.92,-511.88 49.63,-625.65l18.01 -68.91c1.2,-16.21 0.44,-46.35 0.46,-63.83l-57.56 -844.94c-0.84,-7.38 -4.67,-8.34 -6.96,-12.54 26.07,-116.75 -105.09,-627.21 -147.62,-748.7 -44.49,-203.88 -171.61,-515.12 -260.39,-719.57l-43.9 -86.69c-5.09,-21.56 -29.49,-76.26 -61.05,-140.42z"/>
  <g transform="matrix(1.39393 0 0 1 -10074.7 1465.8)">
   <text x="18062.22" y="9031.11"  class="fil2 fnt0">unoApi</text>
  </g>
  <path class="fil3" d="M5856.79 7941.44c0.29,-441.45 0.08,-882.94 -0.27,-1324.4 -0.27,-336.47 35.92,-996.91 -18.26,-1294.83 -99.4,-136.63 -228.78,-255.95 -424.01,-147.53 -189.01,104.96 -157.88,306.18 -157.77,496.65l-2.63 663.5 -3.09 607.94 -3.89 1051.97 0.99 973.69 14.15 655.88c26.7,100.25 32.15,195.54 63.5,297.19 26.67,86.53 78.14,177.39 78.58,262.97 83.24,72.93 168.33,249 220.29,357.15 88.72,49.28 178.5,222.43 309.57,289.46 -89.89,-797.28 -76.58,-565.59 -76.04,-1400.55 0.33,-496.33 -0.74,-992.77 -1.12,-1489.09zm1047.39 -2804.7l-5.51 -12.75c1.89,3.5 3.71,7.74 5.51,12.75l0.03 0.07 14.76 65.13 12.01 101.86 6.57 86 12.25 276.88 5.78 273.61 3.24 481.42 -3.14 726.57 -7.58 742.64 -10.01 879.85 -3.91 998.79 0.68 67.95 14.98 397.89c13.06,208.67 47.23,393.39 126.35,584.79 42.76,103.44 218.26,389.65 229.72,436.09 769.42,12.32 1454.98,-707.77 1636.21,-1416.93 88.15,-344.95 48.42,-2469.55 48.85,-2985.88 0.38,-466.78 -57.81,-925.82 161.5,-1344.59 586.39,-1119.62 2339.05,-951.22 2597.56,445.56 39.15,333.75 15.57,766.77 15.91,1111.81 0.38,370.41 -0.03,740.83 1.44,1111.22 0.92,227.58 34.71,2176.34 -27.02,2209.33 46.56,415.81 -274.36,1091.34 -506.21,1434.72 -240.82,356.68 -628.59,711.33 -1024.78,915.23 -494.55,254.51 -1008.18,386.25 -1575.12,349.9 -737.22,-47.26 -935.3,-233.94 -1456.7,-485.74l-323.46 -253.06c-207.81,-12.23 -320.54,-52.98 -511.38,-81.58 -134.39,-73.77 -385.37,-131.73 -561.06,-223.88 -182.48,-95.7 -327.26,-190.94 -486.97,-316.48 -219.48,-172.52 -638.87,-594.68 -734.86,-876.69 -163.36,-111.92 -357.5,-929.38 -373.78,-1158.5 -42.05,-312.02 -17.04,-760.34 -17.82,-1087.68 -0.89,-370.3 4,-741.5 0.76,-1111.22 -2.11,-240.65 -49.06,-2143.1 24.15,-2193.98 -39.27,-281.04 215.1,-692.42 405.45,-867.75 1028.79,-947.51 2307.85,64.82 2294.22,685.7l11.38 20.95zm3173.19 2638.79c0.33,1726.43 235.39,3108.53 -1471.02,4195.52l30.38 26.97c707.01,47.56 1319.39,-249.93 1709.72,-804.14 433.76,-615.87 351.27,-1474.32 333.9,-2206.87 -53.86,-791.36 49.14,-1987.84 0.36,-2819.44 -12.09,-206.07 -177.54,-350.8 -386.68,-295.32 -221.49,58.76 -217.34,291.44 -216.95,484.72 0.93,472.82 0.2,945.75 0.29,1418.56zm-1398.65 -5992.72l770.77 129.25c44.62,33.88 161.79,53.45 218.62,69.26l612.17 206.19c342.27,130.18 514.11,246.14 755.98,348.59 55.81,41.53 147.27,82.98 215.32,126.57 392.91,251.72 436.52,277.08 806.48,561.87 327.57,252.17 676.27,628.4 944.45,941.89 132.52,154.9 392.93,521.79 493.2,686.21 33.36,54.71 227.48,401.9 246.35,414.18 41.54,124.82 236.26,484.98 341.49,775.22 48.64,134.15 99.21,276.62 138.62,411.26 23.96,81.85 86.42,383.48 124.94,425.17l116.45 755.89c30.97,337 39.78,624.67 35.22,965.95l-27.53 522.24c-36.78,53.58 -60.57,349.01 -77.53,443.95 -26.41,147.89 -62.7,285.82 -92.83,431.83 -95.28,286.36 -150.61,540.65 -265.21,813.6 -56.97,135.68 -107.53,257.86 -165.72,385.25 -42.91,93.93 -151.24,274.32 -175.19,354.63 -25.07,27.81 -16.38,17.16 -34.17,49.43 -6.23,11.3 -18.71,34.01 -22.34,41.3 -23.13,46.41 -15.33,40.81 0,97.05l160.77 592.8c20.82,57.79 14.48,30.47 26.31,46.46 -5.63,57.08 366.37,1408.34 414.94,1552.04 27.57,135.28 74.67,276.05 111.27,413.72l81.41 295.81c20.7,76.71 6.93,53.39 37.86,94 -7.66,105.14 70.82,256.64 83.98,370.74l-373.14 -95.01 -816.6 -210.37c-110.3,-42.73 -1462.65,-394.47 -1526.02,-391.55 -15.5,-12.3 5.89,-6.87 -43.71,-25.03l-149.46 -37.51c-140.41,-38.34 -507.99,-143.51 -609.15,-153.35 -230.92,130.73 -494.45,243.45 -755.66,350.27 -273.56,111.87 -531.32,169.58 -818.78,260.32 -81.69,15.26 -800.37,149.65 -808.11,156.52l-763.8 34.51c-262.86,-0.51 -524.01,-11.61 -783.23,-43.89l-750.71 -118.46 -60.82 -28.05c-15.5,-4.19 -33.98,-6.66 -50.34,-10.64 -39.45,-9.61 -76.04,-20.9 -112.29,-30.64 -74.14,-19.91 -144.25,-41.11 -218.3,-64.16 -140.31,-43.67 -274.48,-91.6 -409.24,-141.18 -314.27,-115.66 -624.55,-295.56 -767.84,-346.43 -21.71,-22.49 -356.27,-213.86 -413.48,-248.7 -421.43,-256.6 -483.48,-360.76 -758.52,-562.36l-344.2 -316.34c-199.3,-202.47 -293.25,-292.37 -479.01,-527.89 -188.75,-239.29 -229.67,-273.36 -421.55,-568.4 -37.02,-56.92 -222.52,-377.32 -248.95,-398.82 -38.24,-111.06 -134.73,-265.17 -191.6,-387.22 -127.18,-272.95 -229.24,-524.56 -321.19,-811.15 -26.05,-81.17 -99.59,-391.15 -135.6,-431.68l-123.07 -697.21c-26.68,-286.27 -54.09,-537.78 -58.14,-831.52l37.74 -789.85c35.03,-281.29 93.43,-534.89 133.11,-800.08 41.97,-102.01 74.88,-287.88 113.32,-407.04l290.92 -778.12c44.57,-104.62 137.36,-256.01 164.84,-349.63 44.87,-56.46 88.2,-150.77 127.33,-222.95l714 -1016.03c27.46,-35.97 53.92,-53.62 82.74,-89.72 109.78,-137.48 190.85,-180.05 238.43,-270.79 26.03,-7.98 18.25,-1.25 25.31,-25.69l556.88 -486.99c61.79,-54.12 129.8,-103.96 194.83,-151.27l735.21 -475.57c19.93,-10.26 41.52,-19.88 61.69,-32.17l38.21 -29.41c1.61,-5.74 5.71,-6.65 8.65,-9.91l738.16 -324.15c258.44,-107.72 708.04,-211.01 802,-251.85 266.57,-41.32 516.99,-109.13 802.28,-138.16l425.25 -36.06c414.47,-9.32 735.83,1.05 1145.53,39.03z"/>
  <path class="fil4" d="M22749.23 11348.4l6910.15 0 0 753.31 -6910.15 0 0 -753.31zm10992.02 899.43l402.34 0c263.44,0 392.77,-110.87 392.77,-314.46l0 -643.47c0,-203.58 -129.33,-314.46 -392.77,-314.46l-402.34 0 0 1272.39zm263.44 -181.76l0 -908.85 134.11 0c83.83,0 134.12,32.71 134.12,123.6l0 661.64c0,90.88 -50.29,123.61 -134.12,123.61l-134.11 0zm-843.01 199.94c258.65,0 392.76,-116.33 392.76,-319.91l0 -970.66 -253.86 0 0 985.2c0,90.88 -50.29,123.6 -134.11,123.6 -83.83,0 -134.11,-32.72 -134.11,-123.6l0 -985.2 -263.45 0 0 970.66c0,203.58 134.12,319.91 392.77,319.91zm-960.36 0c258.65,0 399.95,-116.33 399.95,-319.91l0 -668.92c0,-203.58 -141.3,-319.92 -399.95,-319.92 -258.66,0 -399.96,116.34 -399.96,319.92l0 668.92c0,203.58 141.3,319.91 399.96,319.91zm0 -181.77c-83.83,0 -136.52,-34.53 -136.52,-125.42l0 -694.36c0,-90.89 52.69,-125.42 136.52,-125.42 83.82,0 136.51,34.53 136.51,125.42l0 694.36c0,90.89 -52.69,125.42 -136.51,125.42zm-1195.07 163.59l696.92 0 0 -181.76 -433.48 0 0 -1090.63 -263.44 0 0 1272.39zm-546.04 18.18c253.86,0 387.98,-116.33 387.98,-312.64l0 -179.96 -249.07 0 0 194.5c0,83.61 -52.69,116.33 -131.72,116.33 -79.04,0 -131.72,-32.72 -131.72,-116.33l0 -710.73c0,-83.61 52.68,-118.14 131.72,-118.14 79.03,0 131.72,34.53 131.72,118.14l0 147.24 249.07 0 0 -134.51c0,-196.31 -134.12,-312.65 -387.98,-312.65 -253.86,0 -387.97,116.34 -387.97,312.65l0 683.46c0,196.31 134.11,312.64 387.97,312.64zm-11370.34 -919.06l3066.33 0 0 723.34 -3057.25 12.18 -9.08 -735.52zm-3648.13 -15.72l2924.8 15.72 0 707.61 -2917.4 2.01 -7.4 -725.34z"/>
 </g>
</svg>

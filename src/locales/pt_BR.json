{"without_whatsapp": "O número de telefone %s não tem whatsapp!", "invalid_phone_number": "O número telefone %s esta inválido!", "offline_session": "<PERSON><PERSON><PERSON>, tentando conectar...", "disconnected_session": "<PERSON><PERSON><PERSON> desconectada, por favor envie um mensagem e leia o qrcode se for necessário", "reloaded_session": "<PERSON><PERSON><PERSON> terminada, envie uma mensagem para conectar novamente", "connecting_session": "Espere um momento, conectando a sessão...", "invalid_link": "Houve um erro ao recuperar a midia, status %s para o link %s", "attempts_exceeded": "O limite de %s vezes de geração de qrcode foi excedida!", "received_pending_notifications": "Recebendo mensagens enviadas enquanto estava offline", "online_session": "Sessão está online", "connection_timed_out": "A sessão %s excedeu o tempo de %s ms para conectar, sessão alterada para estado Offline", "connecting": "Conectando...", "connected": "Conectado com o número %s utilizando a versao do Whatsapp v%s, e a útima versão seria v%s, hora atual %s", "removed": "A sessão foi removida no aplicativo do Whatsapp, envie uma mesagem para gerar o qrcode e conectar novamente!", "unique": "A sessão só pode ser conectado uma vez, saindo da atual, envia uma mensagem para conectar novamente!", "closed": "A sessão for encerrada com status: %s, detalhe: %s!", "connecting_attemps": "Tentativa de conexão %s de %s...", "qrcode_attemps": "Por favor, leia QR Code para conectar, tentativa %s de %s", "auto_restart": "Configura para reiniciar a sessão a cada %s milliseconds.", "failed_decrypt": "🕒 Não foi possível ler a mensagem. Peça para enviar novamente ou abra o Whatsapp no celular.", "error": "Erro não tratado: %s.", "on_read_qrcode": "<PERSON><PERSON><PERSON>, le<PERSON> o qrcode, se ainda não leu. O token de authenticação para essa sessão é %s", "pairing_code": "Informe o código para conectar no whatsapp: %s", "restart": "<PERSON><PERSON><PERSON><PERSON>", "standby": "Sessão colocada em standby, esperando pelo tempo configurado para tentar conectar novamente: %s", "proxy_error": "Erro ao conectar no proxy: %s", "session_conflict": "O número the sessão usado é o %s mas o número da configuração é %s", "waiting_information": "Esperando por qrcode/pairing code", "reload": "<PERSON><PERSON><PERSON><PERSON>"}
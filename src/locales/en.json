{"without_whatsapp": "The phone number %s does not have Whatsapp account!", "invalid_phone_number": "The phone number %s is invalid!", "offline_session": "offline session, connecting....", "disconnected_session": "disconnect number, please send a message do try reconnect and read qr code if necessary", "reloaded_session": "Session reloaded, send a message to connect again", "connecting_session": "Wait a moment, connecting process", "invalid_link": "Error on retrieve media, http status %s in link %s", "attempts_exceeded": "The %s times of generate qrcode is exceeded!", "received_pending_notifications": "Received pending notifications", "online_session": "Online session", "connection_timed_out": "Connecting %s timed out %s ms, change to disconnect", "connecting": "Connecting...", "connected": "Connected with %s using Whatsapp Version v%s, latest Baileys version is v%s at %s", "removed": "The session is removed in Whatsapp App, send a message here to reconnect!", "unique": "The session must be unique, close connection, send a message here to reconnect if him was offline!", "closed": "The connection is closed with status: %s, detail: %s!", "connecting_attemps": "Try connnecting time %s of %s...", "qrcode_attemps": "Please, read the QR Code to connect on Whatsapp Web, attempt %s of %s", "auto_restart": "Config to auto restart in %s milliseconds.", "failed_decrypt": "🕒 The message could not be read. Please ask to send it again or open WhatsApp on your phone.", "error": "Error -> %s.", "on_read_qrcode": "Awesome, read the qrcode if you not yet. For now you need to update config to use this auth token %s", "pairing_code": "Open your WhatsApp and go to: Connected Devices > Connect a new Device > Connect using phone number > And put your connection code > %s", "restart": "Restarting session", "standby": "Standby session, waiting for time configured to try connect again, %s error in %s seconds", "proxy_error": "Error on connect to proxy: %s", "session_conflict": "The session number is %s but the configured number %s", "waiting_information": "Waiting for qrcode/pairing code", "reload": "Reload"}
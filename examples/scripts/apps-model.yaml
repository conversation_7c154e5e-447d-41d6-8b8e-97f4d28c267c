version: "3.7"

services:

## --------------------------- REDIS --------------------------- ##
  redis:
    image: redis:latest
    command: [
        "redis-server",
        "--appendonly",
        "yes",
        "--port",
        "6379",
        "--requirepass",
        "${REDIS_PASS}"
      ]
    environment:
      REDIS_PASSWORD: ${REDIS_PASS}
    volumes:
      - redis_data:/data
    networks:
      - ${DOCKERNETWORK}
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 512M

##REDIS COMANDER - WEB PANEL
  redisWeb:
    image: ghcr.io/joeferner/redis-commander:latest
    restart: always
    networks:
      - ${DOCKERNETWORK}
    environment:
      PORT: 8082
      HTTP_USER: ${REDIS_USER}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASS}
      HTTP_PASSWORD: ${REDIS_PASS}
    depends_on:
      - redis
    labels:
      - traefik.enable=true
      - traefik.http.routers.redisdash.rule=Host(`${REDIS_SUBDOMAIN}`)
      - traefik.http.routers.redisdash.entrypoints=web,websecure
      - traefik.http.services.redisdash.loadbalancer.server.port=8082
      - traefik.http.routers.redisdash.service=redisdash
      - traefik.http.routers.redisdash.tls.certresolver=letsencryptresolver
    deploy:
      resources:
        limits:
          cpus: '1.00'
          memory: 512M
        reservations:
          cpus: '0.10'
          memory: 128M

## --------------------------- RABBITMQ --------------------------- ##
  rabbitmq:
    image: rabbitmq:3-management-alpine
    hostname: rabbitmq
    volumes:
      - rabbit_data:/var/lib/rabbitmq
    restart: always
    networks:
      - ${DOCKERNETWORK}
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS}
    labels:
      - traefik.enable=true
      - traefik.http.routers.rabbitmq.rule=Host(`${RABBITMQ_SUBDOMAIN}`)
      - traefik.http.routers.rabbitmq.entrypoints=websecure
      - traefik.http.routers.rabbitmq.tls.certresolver=letsencryptresolver
      - traefik.http.routers.rabbitmq.priority=1
      - traefik.http.routers.rabbitmq.service=rabbitmq
      - traefik.http.services.rabbitmq.loadbalancer.server.port=15672 
    deploy:
      resources:
        limits:
          cpus: '1.00'
          memory: 512M
        reservations:
          cpus: '0.50'
          memory: 128M
## --------------------------- MINIO --------------------------- ##
  minio:
    image: quay.io/minio/minio:latest
    command: server /data --console-address ":9001"
    networks:
      - ${DOCKERNETWORK}
    volumes:
      - minio_data:/data
    restart: always
    environment:
      - MINIO_ROOT_USER=${MINIO_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_PASS}
      - MINIO_BROWSER_REDIRECT_URL=https://${MINIOWEB_SUBDOMAIN}
      - MINIO_SERVER_URL=https://${MINIOAPI_SUBDOMAIN}
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.10'
          memory: 128M
    labels:
      - traefik.enable=true
      - traefik.http.routers.minio_public.rule=Host(`${MINIOAPI_SUBDOMAIN}`)
      - traefik.http.routers.minio_public.entrypoints=web,websecure
      - traefik.http.routers.minio_public.tls.certresolver=letsencryptresolver
      - traefik.http.services.minio_public.loadbalancer.server.port=9000
      - traefik.http.routers.minio_public.service=minio_public
      - traefik.http.routers.minio_console.rule=Host(`${MINIOWEB_SUBDOMAIN}`)
      - traefik.http.routers.minio_console.entrypoints=web,websecure
      - traefik.http.routers.minio_console.tls.certresolver=letsencryptresolver
      - traefik.http.services.minio_console.loadbalancer.server.port=9001
      - traefik.http.routers.minio_console.service=minio_console

## --------------------------- POSTGRES --------------------------- ##

  postgres:
    image: postgres:17-alpine
    volumes:
      - postgres:/data/postgres
    environment:
      POSTGRES_HOST_AUTH_METHOD: md5
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASS}
    restart: always
    networks:
      - ${DOCKERNETWORK}


volumes:
  #REDIS_VOLUME
  redis_data:

  #RABIT_VOLUME
  rabbit_data:

  #MINIO_VOLUME
  minio_data:

  #POSTGRES_VOLUME
  postgres:

networks:
  ${DOCKERNETWORK}:
    external: true
    name: ${DOCKERNETWORK} 

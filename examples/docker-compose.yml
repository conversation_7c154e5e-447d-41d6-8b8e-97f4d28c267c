version: '3'

x-base: &base
  image: docker.io/clairton/unoapi-cloud:v1.9.0
  entrypoint: echo 'ok!'
  environment:
    AMQP_URL: amqp://${RABBITMQ_DEFAULT_USER}:${RABBITMQ_DEFAULT_PASS}@rabbitmq:5672
    REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
    BASE_URL: ${BASE_URL}
    STORAGE_ACCESS_KEY_ID: ${STORAGE_ACCESS_KEY_ID}
    STORAGE_BUCKET_NAME: ${STORAGE_BUCKET_NAME}
    STORAGE_ENDPOINT:	${STORAGE_ENDPOINT}
    STORAGE_FORCE_PATH_STYLE: ${STORAGE_FORCE_PATH_STYLE}
    STORAGE_SECRET_ACCESS_KEY: ${STORAGE_SECRET_ACCESS_KEY}
    WEBHOOK_HEADER: api_access_token
    WEBHOOK_URL: ${WEBHOOK_URL}
    WEBHOOK_TOKEN: ${WEBHOOK_TOKEN}
    IGNORE_GROUP_MESSAGES: ${IGNORE_GROUP_MESSAGES}
    IGNORE_BROADCAST_STATUSES: ${IGNORE_BROADCAST_STATUSES}
    IGNORE_BROADCAST_MESSAGES: ${IGNORE_BROADCAST_MESSAGES}
    IGNORE_OWN_MESSAGES: ${IGNORE_OWN_MESSAGES}
    UNOAPI_AUTH_TOKEN: ${UNOAPI_AUTH_TOKEN}
    REJECT_CALLS: ${REJECT_CALLS}
    REJECT_CALLS_WEBHOOK: ${REJECT_CALLS_WEBHOOK}
    SEND_CONNECTION_STATUS: ${SEND_CONNECTION_STATUS}
    VIRTUAL_PORT: 9876
    LETSENCRYPT_HOST: ${DOMAIN}
    VIRTUAL_HOST: ${DOMAIN}
    SESSION_TTL: ${SESSION_TTL}
    DATA_TTL: ${DATA_TTL}
    GOOGLE_APPLICATION_CREDENTIALS: ${GOOGLE_APPLICATION_CREDENTIALS}
    LOG_LEVEL: ${LOG_LEVEL}
    UNO_LOG_LEVEL: ${UNO_LOG_LEVEL}
  restart: 'no'
  depends_on:
    - redis
    - rabbitmq
    - minio

x-minio: &minio
  image: quay.io/minio/minio:latest
  command: server --address ":9000" --console-address ":9001" http://minio1/data http://minio2/data http://minio3/data
  restart: always
  environment:
    MINIO_ROOT_USER: ${STORAGE_ACCESS_KEY_ID}
    MINIO_ROOT_PASSWORD: ${STORAGE_SECRET_ACCESS_KEY}
    MINIO_SERVER_URL: https://minio.${DOMAIN}
    MINIO_BROWSER_REDIRECT_URL: https://console.minio.${DOMAIN}
  deploy:
    resources:
      limits:
        cpus: '0.75'
        memory: 512M
      reservations:
        cpus: '0.50'
        memory: 256M
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
    interval: 30s
    timeout: 20s
    retries: 3

services:
  web:
    <<: *base
    entrypoint: yarn web
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.75'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    networks:
      - default
      - external-dns

  worker:
    <<: *base
    entrypoint: yarn worker
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.75'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    networks:
      - default

  rabbitmq:
    image: rabbitmq:3-management-alpine
    hostname: rabbitmq
    volumes:
      - rabbitmq:/var/lib/rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
      VIRTUAL_PORT: 15672
      LETSENCRYPT_HOST: rabbitmq.${DOMAIN}
      VIRTUAL_HOST: rabbitmq.${DOMAIN}
    deploy:
      resources:
        limits:
          cpus: '0.75'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    networks:
      - default
      - external-dns

  redis:
    image: redis:7-alpine
    volumes:
      - redis:/data
    command: redis-server --requirepass ${REDIS_PASSWORD}
    restart: always
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '0.75'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    networks:
      - default
  
  redis-commander:
    image: ghcr.io/joeferner/redis-commander:latest
    restart: always
    environment:
      HTTP_USER: default
      HTTP_PASSWORD: ${REDIS_PASSWORD}
      VIRTUAL_PORT: 8081
      LETSENCRYPT_HOST: redis.${DOMAIN}
      VIRTUAL_HOST: redis.${DOMAIN}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    depends_on:
      - redis
    networks:
      - default
      - external-dns
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
  
  minio1:
    <<: *minio
    volumes:
      - storage:/data
  
  minio2:
    <<: *minio
    volumes:
      - storage3:/data
  
  minio3:
    <<: *minio
    volumes:
      - storage3:/data

  minio:
    image: caddy:2-alpine
    restart: always
    command: caddy reverse-proxy --insecure -v --from :80 --to minio1:9000,minio2:9000,minio3:9000
    environment:
      VIRTUAL_PORT: 80
      LETSENCRYPT_HOST: minio.${DOMAIN}
      VIRTUAL_HOST: minio.${DOMAIN}
    depends_on:
      - minio1
      - minio2
      - minio3
    networks:
      - default
      - external-dns

  minio-console:
    image: caddy:2-alpine
    restart: always
    command: caddy reverse-proxy --insecure -v --from :80 --to minio1:9001,minio2:9001,minio3:9001
    environment:
      VIRTUAL_PORT: 80
      LETSENCRYPT_HOST: console.minio.${DOMAIN}
      VIRTUAL_HOST: console.minio.${DOMAIN}
    depends_on:
      - minio1
      - minio2
      - minio3
    networks:
      - default
      - external-dns

  minio-mc:
    image: quay.io/minio/mc:latest
    environment:
      STORAGE_ACCESS_KEY_ID: ${STORAGE_ACCESS_KEY_ID}
      STORAGE_BUCKET_NAME: ${STORAGE_BUCKET_NAME}
      STORAGE_ENDPOINT:	${STORAGE_ENDPOINT}
      STORAGE_SECRET_ACCESS_KEY: ${STORAGE_SECRET_ACCESS_KEY}
    restart: 'no'
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set local $STORAGE_ENDPOINT $STORAGE_ACCESS_KEY_ID $STORAGE_SECRET_ACCESS_KEY;
      /usr/bin/mc mb local/$STORAGE_BUCKET_NAME;
      "
    networks:
      - default

volumes:
  rabbitmq:
  redis:
  storage:
  storage2:
  storage3:

networks:
  external-dns:
    external: true
  

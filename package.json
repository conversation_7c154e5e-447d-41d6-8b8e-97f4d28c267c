{"name": "unoapi-cloud", "version": "2.4.1", "description": "Unoapi Cloud", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["./dist"], "repository": "github.com/clairton/unoapi-cloud", "author": "<PERSON><PERSON><<EMAIL>>", "license": "GPL-3.0", "private": false, "scripts": {"postinstall": "./node_modules/typescript/bin/tsc -p ./node_modules/baileys && cp node_modules/baileys/src/Defaults/baileys-version.json node_modules/baileys/lib/Defaults/baileys-version.json", "lint": "eslint", "test": "node_modules/jest/bin/jest.js --coverage", "build": "./node_modules/typescript/bin/tsc -p .", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "dev": "./node_modules/nodemon/bin/nodemon.js src/index.ts --trace-deprecation", "web-dev": "./node_modules/nodemon/bin/nodemon.js src/web.ts", "waker-dev": "./node_modules/nodemon/bin/nodemon.js src/waker.ts", "standalone-dev": "./node_modules/nodemon/bin/nodemon.js src/standalone.ts", "worker-dev": "./node_modules/nodemon/bin/nodemon.js src/worker.ts", "bulker-dev": "./node_modules/nodemon/bin/nodemon.js src/bulker.ts", "broker-dev": "./node_modules/nodemon/bin/nodemon.js src/broker.ts", "bridge-dev": "./node_modules/nodemon/bin/nodemon.js src/bridge.ts", "cloud-dev": "./node_modules/nodemon/bin/nodemon.js src/cloud.ts", "start": "node dist/src/index.js", "start-ts": "node --experimental-strip-types --experimental-transform-types src/start.ts", "cloud": "node dist/src/cloud.js", "cloud-ts": "node --experimental-strip-types --experimental-transform-types src/cloud.ts", "standalone": "node dist/src/standalone.js", "standalone-ts": "node --experimental-strip-types --experimental-transform-types src/standalone.ts", "web": "node dist/src/web.js", "waker": "node dist/src/waker.js", "web-ts": "node --experimental-strip-types --experimental-transform-types src/web.ts", "worker": "node dist/src/worker.js", "worker-ts": "node --experimental-strip-types --experimental-transform-types src/worker.ts", "bulker": "node dist/src/bulker.js", "bulker-ts": "node --experimental-strip-types --experimental-transform-types src/bulker.ts", "broker": "node dist/src/broker.js", "broker-ts": "node --experimental-strip-types --experimental-transform-types src/broker.ts", "bridge": "node dist/src/bridge.js", "bridge-ts": "node --experimental-strip-types --experimental-transform-types src/bridge.ts", "clean:data": "rm -rf ./data/medias/* ./data/sessions/* ./data/stores/*.json"}, "devDependencies": {"@eslint/js": "^9.1.1", "@types/express": "^4.17.21", "@types/i18n": "^0.13.12", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/node": "^20.12.7", "@types/node-fetch": "2", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "@types/vcf": "^2.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.56.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jest-mock-extended": "^3.0.6", "nodemon": "^3.1.0", "pino-pretty": "^7.0.0", "prettier": "^3.2.5", "supertest": "^7.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.5", "typescript-eslint": "^7.8.0"}, "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@aws-sdk/client-s3": "^3.598.0", "@aws-sdk/s3-request-presigner": "^3.598.0", "@google-cloud/text-to-speech": "^5.2.0", "@redis/client": "^1.5.14", "amqplib": "^0.10.4", "awesome-phonenumber": "^6.8.0", "baileys": "github:whiskeysockets/baileys#v6.7.18", "dotenv": "^16.4.5", "express": "^4.19.2", "i18n": "^0.15.1", "jimp": "^0.22.12", "jschardet": "^3.1.2", "link-preview-js": "^3.0.5", "mime-types": "^2.1.35", "node-cache": "^5.1.2", "node-fetch": "^2.7.0", "node-xlsx": "^0.24.0", "pino": "^7.0.0", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "sharp": "^0.34.1", "socket.io": "^4.7.5", "socks-proxy-agent": "^8.0.3", "uuid": "^9.0.1", "vcf": "^2.1.2", "voice-calls-baileys": "^1.0.5", "xlsx": "^0.18.5", "yaml": "^2.7.0"}}
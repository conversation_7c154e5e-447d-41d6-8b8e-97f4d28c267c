{"parser": "@typescript-eslint/parser", "extends": ["plugin:@typescript-eslint/recommended", "prettier", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "env": {"node": true}, "rules": {"no-var": "error", "indent": ["error", 2, {"SwitchCase": 1}], "no-console": "error", "no-multi-spaces": "error", "space-in-parens": "error", "no-multiple-empty-lines": "error", "prefer-const": "error"}}